/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_rlink.h
 * @description: Relay链路接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_RLINK_H
#define SK_RLINK_H
#include <stdint.h>

enum {
    RLINK_LINK_RUN = 0,
    RLINK_LINK_STOP = 1,
};

enum {
    RLINK_TASK_RUN = 0,
    RLINK_TASK_STOP = 1,
};

enum {
    RLINK_EVENT_STOP_CALL = 1,
    RLINK_EVENT_TX_DATA = 2,
    RLINK_EVENT_RX_JSON_DATA = 3,      // 接收到JSON数据
};

typedef int32_t (*SkRlinkCodedDataCallback)(void *private, uint16_t sessionId,
    uint8_t *data, int32_t len, SkAudioDownlinkTimeRecord *timeRecord);

typedef void (*SkRlinkCodedDataEndCallback)(void *private, uint16_t sessionId);

typedef void* SkRlinkHandler;

void SkRlinkInit();
int SkRlinkEventNotify(uint8_t event, uint16_t param1);
void SkRlinkFeedReordAudio(void *handler, const uint8_t *data, size_t len, uint32_t timestamp);
void SkRlinkSetCodedDataCallback(SkRlinkCodedDataCallback handler, void *private);
void SkRlinkSetCodedDataEndCallback(SkRlinkCodedDataEndCallback handler, void *private);
SkRlinkHandler SkRlinkGetHandler();
void SkRlinkSetFunFlag(uint8_t flag);
void SkRlinkFeedWebSocketAudio(void *arg, void *data, uint16_t len);
void SkRlinkFeedWebSocketText(void *arg, void *data, uint16_t len);

// 新格式JSON消息发送接口
int32_t SkRlinkSendJsonMessage(const char *action, const char *operate, const char *body);
int32_t SkRlinkSendMusicCommand(const char *operate, const char *text);
int32_t SkRlinkSendStoryCommand(const char *operate, const char *text);
int32_t SkRlinkSendCallCommand(const char *operate, const char *text);
int32_t SkRlinkSendCmdCommand(const char *operate, const char *text);
int32_t SkRlinkSendTestCommand(const char *operate, const char *body);

#endif //SK_RLINK_H