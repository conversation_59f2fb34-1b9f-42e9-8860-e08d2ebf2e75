/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_rlink.c
 * @description: Relay链路, 用于和转发模块间传递音频和控制数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "sdkconfig.h"
#include "esp_log.h"
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include "sk_common.h"
#include "sk_websocket.h"
#include "sk_frame.h"
#include "sk_audio_buffer.h"
#include "sk_rlink.h"
#include "sk_os.h"
#include "sk_sm.h"
#include "cJSON.h"

#define RLINK_MSG_QUEUE_SIZE 64
#define TAG "SkRlink"

enum {
    RLINK_SOCK_NOT_RDY = 0,
    RLINK_SOCK_RDY = 1,
};

typedef struct {
    uint8_t event;      // 事件
    uint8_t src;        // 事件源模块
    uint16_t param1;    // 参数
    uint32_t timestamp;
    void    *arg;       // 数据长度
} RlinkMsg;

typedef struct {
    // 基本连接管理
    uint32_t linkFlag;
    char serverIp[16];
    uint16_t port;
    int sock;
    uint16_t sockRdy;
    uint8_t taskFlag;
    TaskHandle_t txTaskHandle;
    QueueHandle_t msgQueue;
    void *recordQueue;
    void *decPrivate;
    
    uint16_t seqID;
    uint16_t sessionID;
    uint32_t sendSuccBytes;
    uint32_t sendFailBytes;

    uint32_t recvDataCnt;
    uint32_t recvBufFailCnt;
    uint32_t recvEnqueueFailCnt;
    uint32_t recvEnqueueCnt;
    SkRlinkCodedDataCallback codedDataCallback;
    SkRlinkCodedDataEndCallback codedDataEndCallback;
} RlinkCtrlInfo;

// 函数声明
static int32_t ProcessNewJsonCommand(RlinkCtrlInfo *ctrl, cJSON *data, const char *sessionId);
static int32_t ProcessMusicCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId);
static int32_t ProcessStoryCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId);
static int32_t ProcessCallCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId);
static int32_t ProcessMockStoryCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId);

RlinkCtrlInfo g_rlinkCtrl = {
    .linkFlag = RLINK_LINK_STOP,
    .sockRdy = RLINK_SOCK_NOT_RDY,
    .taskFlag = RLINK_TASK_RUN,
    .sock = -1,
    .msgQueue = NULL,
};

int RlinkConnect(RlinkCtrlInfo *ctrl) {
	int ret, sock;
	uint32_t addr;
    struct sockaddr_in ack_sock_addr;

    memset(&ack_sock_addr, 0, sizeof(struct sockaddr));
    ack_sock_addr.sin_family = AF_INET;
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);

	addr = inet_addr(ctrl->serverIp);
	memcpy((char *)&ack_sock_addr.sin_addr, (char *)&addr, sizeof(addr));
    ack_sock_addr.sin_port = htons(ctrl->port);
    struct timeval send_timeout = {0, 100000};

    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &send_timeout, sizeof(send_timeout)) < 0) {
        perror("Failed to set SO_SNDTIMEO");
        close(sock);
        return -1;
    }

	ret = connect(sock, (struct sockaddr *)&ack_sock_addr, sizeof(struct sockaddr));
    if (ret != 0) {
        ESP_LOGI(TAG, "connect ack failed! socket num=%d", sock);
        closesocket(sock);
        return SK_RET_FAIL;
    }
	ctrl->sock = sock;
	ctrl->sockRdy = RLINK_SOCK_RDY;
    ctrl->sendSuccBytes = 0;
    ctrl->sendFailBytes = 0;

    return SK_RET_SUCCESS;
}

void RlinkDisconnect(RlinkCtrlInfo *ctrl) {
	if (ctrl->sockRdy == RLINK_SOCK_RDY) {
		ctrl->sockRdy = RLINK_SOCK_NOT_RDY;
	    closesocket(ctrl->sock);
		ctrl->sock = -1;
	}

    return;
}

int RlinkSendInnerEvent(RlinkCtrlInfo *ctrl, uint8_t event, uint16_t param1) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        ESP_LOGI(TAG, "send inner event %d, msgQueue is null", event);
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.src = 0;
    msg.arg = NULL;
    msg.param1 = param1;
    if (xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY) != pdPASS) {
        ESP_LOGI(TAG, "send inner event %d success, error", event);
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "send inner event %d success", event);
    return SK_RET_SUCCESS;
}

int SkRlinkEventNotify(uint8_t event, uint16_t param1) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.param1 = param1;
    msg.arg = NULL;
    xQueueSend(g_rlinkCtrl.msgQueue, &msg, portMAX_DELAY);
    return SK_RET_SUCCESS;
}

int SkRlinkSendAudioData(SkAudioBuf *audioBuf, uint32_t timestamp) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = RLINK_EVENT_TX_DATA;
    msg.src = 0;
    msg.arg = audioBuf;
    msg.timestamp = timestamp;
    if (xQueueSend(g_rlinkCtrl.msgQueue, &msg, 0) != pdPASS) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int RlinkSendRemoteMsg(RlinkCtrlInfo *ctrl, void *data, size_t size) {
    int ret;

    ret = send(ctrl->sock, (char *)data, size, 0);
    if (ret != size) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

inline uint16_t RlinkGetTxSeqId(RlinkCtrlInfo *ctrl) {
    ctrl->seqID++;
    return ctrl->seqID;
}

int32_t RlinkSendAudioFrame(RlinkCtrlInfo *ctrl, DataFrame *frame, uint32_t dataLen, uint32_t timestamp) {
    int ret;
    uint16_t seq;
    SkAudioUplinkTimeRecord *timeRecord = (SkAudioUplinkTimeRecord *) frame->timeRecord;

    seq = RlinkGetTxSeqId(ctrl);
    timeRecord->seq = seq;
    timeRecord->len = dataLen;
    timeRecord->dataFlag = frame->data[1];
    timeRecord->encDoneTick = timestamp;
    timeRecord->encTxTick = SkOsGetTickCnt();
    EncodeRelayDataMsg(frame, dataLen + sizeof(frame->timeRecord));
    frame->frameHead.seqID = htons(seq);

    ret = RlinkSendRemoteMsg(ctrl, frame, dataLen + sizeof(FrameHead) + sizeof(frame->timeRecord));
    if (ret != SK_RET_SUCCESS) {
        // 简化错误处理：连续失败时停止链路
        static int failCnt = 0;
        failCnt++;
        if (failCnt >= 8) {
            ctrl->linkFlag = RLINK_LINK_STOP;
        }
    } 

    return ret;
}

int RlinkSendAudioData(RlinkCtrlInfo *ctrl, SkAudioBuf *audioBuf, uint32_t timestamp) {
    int ret = SK_RET_SUCCESS;

    if (ctrl->linkFlag == RLINK_LINK_RUN) {
        ret = RlinkSendAudioFrame(ctrl, (DataFrame *)audioBuf->data,
            audioBuf->length, timestamp);
    }
    SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);

    return ret;
}

int RlinkInit() {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;

    ctrl->msgQueue = xQueueCreate(RLINK_MSG_QUEUE_SIZE, sizeof(RlinkMsg));
    ctrl->recordQueue = SkCreateAudioQueue(4, sizeof(DataFrame), sizeof(FrameHead));

    return SK_RET_SUCCESS;
}

void RlinkDeinit(void) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    vQueueDelete(ctrl->msgQueue);
}

/**
 * @brief 处理新格式JSON命令消息
 * @param ctrl rlink控制结构体
 * @param data JSON数据对象
 * @param sessionId 会话ID
 * @return SK_RET_SUCCESS成功，其他失败
 */
static int32_t ProcessNewJsonCommand(RlinkCtrlInfo *ctrl, cJSON *data, const char *sessionId) {
    if (data == NULL) {
        ESP_LOGW(TAG, "Command message missing data field");
        return SK_RET_INVALID_PARAM;
    }

    // 提取action字段
    cJSON *action = cJSON_GetObjectItem(data, "action");
    if (!cJSON_IsString(action)) {
        ESP_LOGW(TAG, "Command message missing action field");
        return SK_RET_INVALID_PARAM;
    }

    // 提取operate字段
    cJSON *operate = cJSON_GetObjectItem(data, "operate");
    if (!cJSON_IsString(operate)) {
        ESP_LOGW(TAG, "Command message missing operate field");
        return SK_RET_INVALID_PARAM;
    }

    // 提取text字段（可选）
    cJSON *text = cJSON_GetObjectItem(data, "text");
    const char *textStr = (text && cJSON_IsString(text)) ? text->valuestring : "";

    ESP_LOGI(TAG, "Received command - action: %s, operate: %s, text: %s, sessionId: %s",
             action->valuestring, operate->valuestring, textStr, sessionId ? sessionId : "null");

    // 根据action和operate处理命令
    if (strcmp(action->valuestring, "music") == 0) {
        return ProcessMusicCommand(ctrl, operate->valuestring, textStr, sessionId);
    } else if (strcmp(action->valuestring, "story") == 0) {
        return ProcessStoryCommand(ctrl, operate->valuestring, textStr, sessionId);
    } else if (strcmp(action->valuestring, "call") == 0) {
        return ProcessCallCommand(ctrl, operate->valuestring, textStr, sessionId);
    } else if (strcmp(action->valuestring, "mock_story") == 0) {
        return ProcessMockStoryCommand(ctrl, operate->valuestring, textStr, sessionId);
    } else {
        ESP_LOGW(TAG, "Unknown action: %s", action->valuestring);
        return SK_RET_INVALID_PARAM;
    }
}

/**
 * @brief 处理音乐命令
 */
static int32_t ProcessMusicCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId) {
    ESP_LOGI(TAG, "Processing music command: %s", operate);

    if (strcmp(operate, "start") == 0) {
        ESP_LOGI(TAG, "Music: Start playing");
        // 通知状态机开始音乐播放
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_MUSIC, 0, 0);
    } else if (strcmp(operate, "pause") == 0) {
        ESP_LOGI(TAG, "Music: Pause");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_PAUSE, 0, 0);
    } else if (strcmp(operate, "resume") == 0) {
        ESP_LOGI(TAG, "Music: Resume");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_RESUME, 0, 0);
    } else if (strcmp(operate, "stop") == 0) {
        ESP_LOGI(TAG, "Music: Stop");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_QUIT, 0, 0);
    } else if (strcmp(operate, "next") == 0) {
        ESP_LOGI(TAG, "Music: Next");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_NEXT, 0, 0);
    } else if (strcmp(operate, "prev") == 0) {
        ESP_LOGI(TAG, "Music: Previous");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_PREV, 0, 0);
    } else {
        ESP_LOGW(TAG, "Unknown music operate: %s", operate);
        return SK_RET_INVALID_PARAM;
    }

    return SK_RET_SUCCESS;
}

/**
 * @brief 处理故事命令
 */
static int32_t ProcessStoryCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId) {
    ESP_LOGI(TAG, "Processing story command: %s", operate);

    if (strcmp(operate, "start") == 0) {
        ESP_LOGI(TAG, "Story: Start playing - %s", text);
        // 可以根据text内容选择具体故事
    } else if (strcmp(operate, "pause") == 0) {
        ESP_LOGI(TAG, "Story: Pause");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_PAUSE, 0, 0);
    } else if (strcmp(operate, "resume") == 0) {
        ESP_LOGI(TAG, "Story: Resume");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_RESUME, 0, 0);
    } else if (strcmp(operate, "stop") == 0) {
        ESP_LOGI(TAG, "Story: Stop");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_QUIT, 0, 0);
    } else if (strcmp(operate, "next") == 0) {
        ESP_LOGI(TAG, "Story: Next");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_NEXT, 0, 0);
    } else if (strcmp(operate, "prev") == 0) {
        ESP_LOGI(TAG, "Story: Previous");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_PREV, 0, 0);
    } else {
        ESP_LOGW(TAG, "Unknown story operate: %s", operate);
        return SK_RET_INVALID_PARAM;
    }

    return SK_RET_SUCCESS;
}

/**
 * @brief 处理通话命令
 */
static int32_t ProcessCallCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId) {
    ESP_LOGI(TAG, "Processing call command: %s", operate);

    if (strcmp(operate, "handshake") == 0) {
        ESP_LOGI(TAG, "Call: Handshake");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_CALL, 0, 0);
    } else if (strcmp(operate, "accept") == 0) {
        ESP_LOGI(TAG, "Call: Accept");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_CONFIRM, 0, 0);
    } else if (strcmp(operate, "reject") == 0) {
        ESP_LOGI(TAG, "Call: Reject");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_QUIT, 0, 0);
    } else if (strcmp(operate, "start") == 0) {
        ESP_LOGI(TAG, "Call: Start");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_CALL, 0, 0);
    } else if (strcmp(operate, "stop") == 0) {
        ESP_LOGI(TAG, "Call: Stop");
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_QUIT, 0, 0);
    } else {
        ESP_LOGW(TAG, "Unknown call operate: %s", operate);
        return SK_RET_INVALID_PARAM;
    }

    return SK_RET_SUCCESS;
}

/**
 * @brief 处理模拟故事命令
 */
static int32_t ProcessMockStoryCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId) {
    ESP_LOGI(TAG, "Processing mock story command: %s, text: %s", operate, text);

    if (strcmp(operate, "start") == 0) {
        ESP_LOGI(TAG, "Mock Story: Start - %s", text);
      
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_CHAT, 0, 0);
    } else {
        ESP_LOGW(TAG, "Unknown mock story operate: %s", operate);
        return SK_RET_INVALID_PARAM;
    }

    return SK_RET_SUCCESS;
}

/**
 * @brief 处理旧格式JSON命令消息（保持兼容性）
 * @param ctrl rlink控制结构体
 * @param data JSON数据对象
 * @return SK_RET_SUCCESS成功，其他失败
 */
static int32_t ProcessJsonCommand(RlinkCtrlInfo *ctrl, cJSON *data) {
    if (data == NULL) {
        ESP_LOGW(TAG, "Command message missing data field");
        return SK_RET_INVALID_PARAM;
    }

    cJSON *cmd = cJSON_GetObjectItem(data, "cmd");
    if (!cJSON_IsString(cmd)) {
        ESP_LOGW(TAG, "Command message missing cmd field");
        return SK_RET_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "Received legacy command: %s", cmd->valuestring);

    if (strcmp(cmd->valuestring, "stop_audio") == 0) {
        ESP_LOGI(TAG, "Command: Stop audio - disabling WebSocket audio reception");
        if (!SkWsIsConnected()) {
            ESP_LOGW(TAG, "WebSocket is already disconnected");
        } else {
            SkWsStopConnect();
            ESP_LOGI(TAG, "WebSocket audio reception stopped");
        }
    } else if (strcmp(cmd->valuestring, "play_music") == 0) {
        ESP_LOGI(TAG, "Command: Play music - requesting music stream from server");
    } else if (strcmp(cmd->valuestring, "music_ready") == 0) {
        ESP_LOGI(TAG, "Server response: Music stream ready - audio will start playing");
    } else if (strcmp(cmd->valuestring, "music_error") == 0) {
        ESP_LOGE(TAG, "Server response: Music playback error");
    } else {
        ESP_LOGW(TAG, "Unknown legacy command: %s", cmd->valuestring);
        return SK_RET_INVALID_PARAM;
    }

    return SK_RET_SUCCESS;
}

/**
 * @brief 处理接收到的JSON消息（支持新旧格式）
 * @param ctrl rlink控制结构体
 * @param jsonStr JSON字符串
 * @return SK_RET_SUCCESS成功，其他失败
 */
static int32_t ProcessJsonMessage(RlinkCtrlInfo *ctrl, const char *jsonStr) {
    ESP_LOGI(TAG, "Processing JSON: %s", jsonStr);

    // 解析JSON
    cJSON *json = cJSON_Parse(jsonStr);
    if (json == NULL) {
        ESP_LOGE(TAG, "Failed to parse JSON");
        return SK_RET_INVALID_PARAM;
    }

    int32_t ret = SK_RET_SUCCESS;

    // 检查是否为新格式（包含code字段）
    cJSON *code = cJSON_GetObjectItem(json, "code");
    if (cJSON_IsNumber(code)) {
        // 新格式处理
        ESP_LOGI(TAG, "Processing new JSON format with code: %d", code->valueint);

        // 检查状态码
        if (code->valueint != 2200) {
            ESP_LOGW(TAG, "Received non-success code: %d", code->valueint);
            // 可以根据不同的错误码进行处理
        }

        // 提取其他字段
        cJSON *data = cJSON_GetObjectItem(json, "data");
        cJSON *message = cJSON_GetObjectItem(json, "message");
        cJSON *sessionId = cJSON_GetObjectItem(json, "sessionId");
        cJSON *sn = cJSON_GetObjectItem(json, "sn");

        const char *messageStr = (message && cJSON_IsString(message)) ? message->valuestring : "";
        const char *sessionIdStr = (sessionId && cJSON_IsString(sessionId)) ? sessionId->valuestring : "";
        int snValue = (sn && cJSON_IsNumber(sn)) ? sn->valueint : 0;

        ESP_LOGI(TAG, "New format - message: %s, sessionId: %s, sn: %d",
                 messageStr, sessionIdStr, snValue);

        // 处理新格式命令
        if (data != NULL) {
            ret = ProcessNewJsonCommand(ctrl, data, sessionIdStr);
        } else {
            ESP_LOGW(TAG, "New format message missing data field");
            ret = SK_RET_INVALID_PARAM;
        }

    } else {
        // 旧格式处理（保持兼容性）
        cJSON *type = cJSON_GetObjectItem(json, "type");
        if (!cJSON_IsString(type)) {
            ESP_LOGE(TAG, "Missing or invalid type field in legacy format");
            cJSON_Delete(json);
            return SK_RET_INVALID_PARAM;
        }

        cJSON *data = cJSON_GetObjectItem(json, "data");
        ESP_LOGI(TAG, "Processing legacy JSON format with type: %s", type->valuestring);

        // 根据消息类型处理
        if (strcmp(type->valuestring, "command") == 0) {
            ret = ProcessJsonCommand(ctrl, data);
        } else if (strcmp(type->valuestring, "config") == 0) {
            ESP_LOGI(TAG, "Received config message");
            ret = SK_RET_SUCCESS;
        } else if (strcmp(type->valuestring, "notification") == 0) {
            ESP_LOGI(TAG, "Received notification message");
            ret = SK_RET_SUCCESS;
        } else {
            ESP_LOGW(TAG, "Unknown message type: %s", type->valuestring);
            ret = SK_RET_INVALID_PARAM;
        }
    }

    cJSON_Delete(json);
    return ret;
}

int RlinkProcLocalMsg(RlinkCtrlInfo *ctrl, TickType_t ticks) {
    int ret = SK_RET_SUCCESS;
    RlinkMsg msg;

    if (xQueueReceive(ctrl->msgQueue, &msg, ticks) != pdPASS) {
        return ret;
    }
    if (msg.event != RLINK_EVENT_TX_DATA) {
        ESP_LOGD(TAG, "RlinkProcLocalMsg: event = %d", msg.event);
    }
    switch (msg.event) {

        case RLINK_EVENT_STOP_CALL:
            ctrl->linkFlag = RLINK_LINK_STOP;
            break;

        case RLINK_EVENT_TX_DATA:
            RlinkSendAudioData(ctrl, (SkAudioBuf *)msg.arg, msg.timestamp);
            break;

        case RLINK_EVENT_RX_JSON_DATA:
            // 处理接收到的JSON数据
            if (msg.arg != NULL) {
                ProcessJsonMessage(ctrl, (const char *)msg.arg);
                free(msg.arg);  // 释放JSON字符串内存
            }
            break;

        default:
            ESP_LOGI(TAG, "Unknown event:%d", msg.event);
    }

    return ret;
}

void SkRlinkSetFunFlag(uint8_t flag) {
    g_rlinkCtrl.taskFlag = flag;
}

void RlinkMainTask(void *arg) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    TickType_t ticks = pdMS_TO_TICKS(100);

    while (ctrl->taskFlag == RLINK_TASK_RUN) {
        // 检查 队列中是否有消息
        if (RlinkProcLocalMsg(ctrl, ticks) != SK_RET_SUCCESS) {
            break;
        }
    }
    vTaskDelete(NULL);
}

void SkRlinkFeedReordAudio(void *handler, const uint8_t *data, size_t len, uint32_t timestamp) {
    RlinkCtrlInfo *ctrl = (RlinkCtrlInfo *)handler;
    SkAudioBuf *audioBuf;

    if (ctrl->linkFlag != RLINK_LINK_RUN) {
        return;
    }
    audioBuf = SkAudioBufferGetFree(ctrl->recordQueue, 0);
    if (audioBuf == NULL) {
        ESP_LOGE(TAG, "Failed to get audio buffer");
        return;
    }
    if (audioBuf->size < (len + audioBuf->offset + 32)) {
        ESP_LOGE(TAG, "SkRlinkFeedReordAudio: audioBuf->size=%d < len=%u", audioBuf->size, len);
        SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);
        return;
    }
    memcpy((int8_t *)&audioBuf->data[audioBuf->offset + 32], data, len);
    audioBuf->length = len;
    if (SkRlinkSendAudioData(audioBuf, timestamp) != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "SkRlinkSendAudioData failed!");
        SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);
    }
    return;
}

void SkRlinkStartTasks() {
    g_rlinkCtrl.taskFlag = RLINK_TASK_RUN;
    xTaskCreate(RlinkMainTask, "RlinkMainTask", 4096, NULL, 5, &g_rlinkCtrl.txTaskHandle);
    ESP_LOGI(TAG, "stack base %p", pxTaskGetStackStart(g_rlinkCtrl.txTaskHandle));
    return;
}

void SkRlinkInit() {
    if (RlinkInit() != SK_RET_SUCCESS) {
        return;
    }
    SkRlinkStartTasks();

    return;
}

void RlinkPlayAudioData(RlinkCtrlInfo *ctrl, uint8_t *data, uint16_t payloadLen) {
    int32_t ret;
    DataFrame *frame = (DataFrame *)data;
    SkAudioDownlinkTimeRecord *timeRecord = (SkAudioDownlinkTimeRecord *)frame->timeRecord;
    SkAudioDownlinkTimeRecord audioTickRecord;

    ctrl->recvDataCnt++;
    if (ctrl->codedDataCallback == NULL) {
        return;
    }
    memcpy(&audioTickRecord, timeRecord, sizeof(SkAudioDownlinkTimeRecord));
    audioTickRecord.decRxTick = SkOsGetTickCnt();
    ret = ctrl->codedDataCallback(ctrl->decPrivate, ctrl->sessionID, 
        data + sizeof(FrameHead) + sizeof(frame->timeRecord), 
        payloadLen - sizeof(frame->timeRecord), &audioTickRecord);
    if (ret == SK_RET_SUCCESS) {
        ctrl->recvEnqueueCnt++;
    } else if (ret == SK_RET_NO_MEMORY) {
        ctrl->recvBufFailCnt++;
    } else if (ret == SK_RET_INVALID_PARAM) {
        ctrl->recvEnqueueFailCnt++;
    }

    return;
}

void SkRlinkSetCodedDataCallback(SkRlinkCodedDataCallback callback, void *private) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    ctrl->codedDataCallback = callback;
    ctrl->decPrivate = private;
    return;
}

void SkRlinkSetCodedDataEndCallback(SkRlinkCodedDataEndCallback callback, void *private) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    ctrl->codedDataEndCallback = callback;
    ctrl->decPrivate = private;
    return;
}

SkRlinkHandler SkRlinkGetHandler() {
    return &g_rlinkCtrl;
}

static SkWsBinaryHeader_t* ValidateWebSocketAudioData(void *data, uint16_t len) {
    const uint16_t headerSize = sizeof(SkWsBinaryHeader_t);  // 8字节头部
    SkWsBinaryHeader_t *pkt;

    // 数据有效性检查
    if (data == NULL || len < headerSize) {
        ESP_LOGE(TAG, "Invalid WebSocket audio data: len=%d, min_len=%d", len, headerSize);
        return NULL;
    }

    pkt = (SkWsBinaryHeader_t *)data;

    // 验证数据格式
    if (pkt->version != 1 || pkt->type != 1) {
        ESP_LOGE(TAG, "Invalid WS packet: ver=%d, type=%d", pkt->version, pkt->type);
        return NULL;
    }

    // 验证负载长度
    uint16_t actualPayloadLen = len - headerSize;
    if (pkt->payloadLen != actualPayloadLen) {
        ESP_LOGE(TAG, "Payload length mismatch: expected=%d, actual=%d",
                pkt->payloadLen, actualPayloadLen);
        return NULL;
    }

    // 检查负载长度是否合理
    if (pkt->payloadLen == 0 || pkt->payloadLen > 1024) {
        ESP_LOGE(TAG, "Invalid payload length: %d", pkt->payloadLen);
        return NULL;
    }

    return pkt;
}

static void ProcessWebSocketAudioData(RlinkCtrlInfo *ctrl, SkWsBinaryHeader_t *pkt, uint16_t totalDataLen) {
    uint8_t *frameBuffer = NULL;
    DataFrame *frame = NULL;
    SkAudioDownlinkTimeRecord *timeRecord;
    uint16_t totalLen;

    // 计算需要的总长度：FrameHead + timeRecord + 4字节头部 + Opus数据
    totalLen = sizeof(FrameHead) + sizeof(SkAudioDownlinkTimeRecord) + 4 + pkt->payloadLen;

    // 分配临时缓冲区
    frameBuffer = (uint8_t *)malloc(totalLen);
    if (frameBuffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer");
        return;
    }

    frame = (DataFrame *)frameBuffer;

    // 构造FrameHead
    frame->frameHead.headFlag = htons(FRAME_HEAD_FLAG);
    frame->frameHead.frameType = FRAME_DPKT_TERM_AND_RELAY;
    frame->frameHead.msgType = MSG_TYPE_INVALID;
    frame->frameHead.payloadLen = htons(sizeof(SkAudioDownlinkTimeRecord) + 4 + pkt->payloadLen);
    frame->frameHead.seqID = htons(pkt->seqNum);

    // 构造时间记录
    timeRecord = (SkAudioDownlinkTimeRecord *)frame->timeRecord;
    memset(timeRecord, 0, sizeof(SkAudioDownlinkTimeRecord));
    timeRecord->seq = pkt->seqNum;
    timeRecord->len = 4 + pkt->payloadLen;  // 包含4字节头部的总长度
    timeRecord->decRxTick = SkOsGetTickCnt();

    // 构造4字节头部
    uint8_t *audioDataPos = frameBuffer + sizeof(FrameHead) + sizeof(SkAudioDownlinkTimeRecord);
    audioDataPos[0] = pkt->seqNum & 0xFF;           // seqNum低字节
    audioDataPos[1] = (pkt->seqNum >> 8) & 0xFF;    // seqNum高字节
    audioDataPos[2] = pkt->payloadLen & 0xFF;       // payloadLen低字节
    audioDataPos[3] = (pkt->payloadLen >> 8) & 0xFF; // payloadLen高字节

    // 复制Opus音频数据（在4字节头部之后）
    memcpy(audioDataPos + 4, pkt->data, pkt->payloadLen);

    // 调用现有的音频处理函数
    RlinkPlayAudioData(ctrl, frameBuffer,
                       sizeof(SkAudioDownlinkTimeRecord) + 4 + pkt->payloadLen);

    // 释放临时缓冲区
    free(frameBuffer);
}

void SkRlinkFeedWebSocketText(void *arg, void *data, uint16_t len) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    RlinkMsg msg;
    char *jsonStr;

    // 验证数据有效性
    if (data == NULL || len == 0 || len > 2048) {
        ESP_LOGE(TAG, "Invalid JSON data: len=%d", len);
        return;
    }

    // 分配内存并复制JSON字符串
    jsonStr = (char *)malloc(len + 1);
    if (jsonStr == NULL) {
        ESP_LOGE(TAG, "Failed to allocate JSON string");
        return;
    }

    memcpy(jsonStr, data, len);
    jsonStr[len] = '\0';

    // 创建消息并放入队列
    msg.event = RLINK_EVENT_RX_JSON_DATA;
    msg.src = 0;
    msg.param1 = 0;
    msg.timestamp = SkOsGetTickCnt();
    msg.arg = jsonStr;

    if (xQueueSend(ctrl->msgQueue, &msg, 0) != pdPASS) {
        ESP_LOGE(TAG, "Failed to send JSON message to queue");
        free(jsonStr);
    }
}

void SkRlinkFeedWebSocketAudio(void *arg, void *data, uint16_t len) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    SkWsBinaryHeader_t *pkt;

    pkt = ValidateWebSocketAudioData(data, len);
    if (pkt == NULL) {
        return;  // 验证失败，错误信息已在验证函数中记录
    }

    ProcessWebSocketAudioData(ctrl, pkt, len);
}

/**
 * @brief 发送新格式JSON消息到云端
 * @param action 动作类型
 * @param operate 操作类型
 * @param body 消息体内容
 * @return SK_RET_SUCCESS成功，其他失败
 */
int32_t SkRlinkSendNewJsonMessage(const char *action, const char *operate, const char *body) {
    if (action == NULL || operate == NULL) {
        ESP_LOGE(TAG, "Invalid parameters for JSON message");
        return SK_RET_INVALID_PARAM;
    }

    // 创建JSON对象
    cJSON *json = cJSON_CreateObject();
    if (json == NULL) {
        ESP_LOGE(TAG, "Failed to create JSON object");
        return SK_RET_NO_MEMORY;
    }

    // 添加字段
    cJSON_AddStringToObject(json, "action", action);
    cJSON_AddStringToObject(json, "operate", operate);

    // 生成requestId（简单递增）
    static uint32_t requestIdCounter = 1;
    char requestIdStr[16];
    snprintf(requestIdStr, sizeof(requestIdStr), "%lu", (unsigned long)requestIdCounter++);
    cJSON_AddStringToObject(json, "requestId", requestIdStr);

    // 添加时间戳（毫秒级）
    uint32_t timestamp = SkOsGetTickCnt();
    cJSON_AddNumberToObject(json, "timestamp", timestamp);

    // 添加消息体
    if (body != NULL && strlen(body) > 0) {
        cJSON_AddStringToObject(json, "body", body);
    } else {
        cJSON_AddStringToObject(json, "body", "");
    }

    // 转换为字符串
    char *jsonString = cJSON_PrintUnformatted(json);
    if (jsonString == NULL) {
        ESP_LOGE(TAG, "Failed to convert JSON to string");
        cJSON_Delete(json);
        return SK_RET_FAIL;
    }

    ESP_LOGI(TAG, "Sending new format JSON: %s", jsonString);

    // 发送JSON消息
    int32_t result = SK_RET_SUCCESS;
    if (SkWsIsConnected()) {
        result = SkWsSendRaw((uint8_t*)jsonString, strlen(jsonString));
        if (result == SK_RET_SUCCESS) {
            ESP_LOGI(TAG, "JSON message sent successfully");
        } else {
            ESP_LOGE(TAG, "Failed to send JSON message, error: %d", result);
        }
    } else {
        ESP_LOGE(TAG, "WebSocket not connected, cannot send JSON message");
        result = SK_RET_NOT_READY;
    }

    // 清理资源
    cJSON_free(jsonString);
    cJSON_Delete(json);

    return result;
}

/**
 * @brief 发送音乐控制命令
 */
int32_t SkRlinkSendMusicCommand(const char *operate, const char *text) {
    return SkRlinkSendNewJsonMessage("music", operate, text ? text : "");
}

/**
 * @brief 发送故事控制命令
 */
int32_t SkRlinkSendStoryCommand(const char *operate, const char *text) {
    return SkRlinkSendNewJsonMessage("story", operate, text ? text : "");
}

/**
 * @brief 发送通话控制命令
 */
int32_t SkRlinkSendCallCommand(const char *operate, const char *text) {
    return SkRlinkSendNewJsonMessage("call", operate, text ? text : "");
}

/**
 * @brief 发送测试命令
 */
int32_t SkRlinkSendTestCommand(const char *operate, const char *body) {
    return SkRlinkSendNewJsonMessage("test", operate, body ? body : "测试");
}
