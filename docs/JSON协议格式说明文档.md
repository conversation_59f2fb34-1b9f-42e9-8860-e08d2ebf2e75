# SmartKid终端 JSON协议格式说明文档

## 📋 概述

本文档详细说明了SmartKid智能终端与云端服务器之间的JSON通信协议格式，包括消息结构、处理流程和使用示例。

## 🔄 协议格式定义

### 1. 接收格式（云端 → 终端）

```json
{
  "code": 2200,                    // 状态码（必填）
  "data": {                        // 数据对象（必填）
    "action": "music",             // 动作类型（必填）
    "operate": "start",            // 操作类型（必填）
    "text": "播放音乐"             // ASR识别文本（可选）
  },
  "message": "SUCCEED",            // 响应消息（必填）
  "sessionId": "b0134564179e486589ba90211ed74071", // 会话ID（必填）
  "sn": 1                         // 序列号（必填）
}
```

### 2. 发送格式（终端 → 云端）

```json
{
  "action": "music",               // 动作类型（必填）
  "operate": "start",              // 操作类型（必填）
  "requestId": "12",               // 请求ID（必填）
  "timestamp": 1753774119,         // 时间戳-毫秒级（必填）
  "body": "Voice command play music" // 消息体内容（必填）
}
```

## 📊 字段说明

### 接收消息字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `code` | Number | ✅ | 状态码，2200表示成功 |
| `data` | Object | ✅ | 包含具体命令数据的对象 |
| `data.action` | String | ✅ | 动作类型（music/story/call等） |
| `data.operate` | String | ✅ | 操作类型（start/pause/stop/next等） |
| `data.text` | String | ❌ | ASR识别的文本内容 |
| `message` | String | ✅ | 响应消息（SUCCEED/ERROR等） |
| `sessionId` | String | ✅ | 会话唯一标识符 |
| `sn` | Number | ✅ | 消息序列号 |

### 发送消息字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `action` | String | ✅ | 动作类型 |
| `operate` | String | ✅ | 操作类型 |
| `requestId` | String | ✅ | 请求唯一标识符（自动生成） |
| `timestamp` | Number | ✅ | 时间戳（毫秒级，自动生成） |
| `body` | String | ✅ | 消息体内容 |

## 🎯 支持的Action类型

### 1. 音乐控制 (music)

| 操作 | 说明 | 示例 |
|------|------|------|
| `start` | 开始播放音乐 | 用户说"播放音乐" |
| `pause` | 暂停音乐播放 | 用户说"暂停" |
| `resume` | 恢复音乐播放 | 用户说"继续播放" |
| `stop` | 停止音乐播放 | 用户说"停止音乐" |
| `next` | 播放下一首 | 用户说"下一首" |
| `prev` | 播放上一首 | 用户说"上一首" |

### 2. 故事播放 (story)

| 操作 | 说明 | 示例 |
|------|------|------|
| `start` | 开始播放故事 | 用户说"讲故事" |
| `pause` | 暂停故事播放 | 用户说"暂停" |
| `resume` | 恢复故事播放 | 用户说"继续" |
| `stop` | 停止故事播放 | 用户说"停止故事" |
| `next` | 播放下一个故事 | 用户说"下一个故事" |
| `prev` | 播放上一个故事 | 用户说"上一个故事" |

### 3. 通话控制 (call)

| 操作 | 说明 | 示例 |
|------|------|------|
| `handshake` | 发起通话握手 | 云端发起呼叫 |
| `accept` | 接受通话 | 用户说"接听" |
| `reject` | 拒绝通话 | 用户说"拒绝" |
| `start` | 开始通话 | 通话建立成功 |
| `stop` | 结束通话 | 用户说"挂断" |

## 🔧 状态码说明

| 状态码 | 含义 | 处理方式 |
|--------|------|----------|
| `2200` | 成功 | 正常处理命令 |
| `4000` | 客户端错误 | 记录错误日志 |
| `5000` | 服务器错误 | 记录错误日志 |

## 💻 代码实现

### 核心处理函数

```c
// 主要的JSON消息处理函数
static int32_t ProcessJsonMessage(RlinkCtrlInfo *ctrl, const char *jsonStr);

// 具体命令处理函数
static int32_t ProcessJsonCommand(RlinkCtrlInfo *ctrl, cJSON *data, const char *sessionId);
static int32_t ProcessMusicCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId);
static int32_t ProcessStoryCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId);
static int32_t ProcessCallCommand(RlinkCtrlInfo *ctrl, const char *operate, const char *text, const char *sessionId);
```

### 发送接口函数

```c
// 通用JSON消息发送
int32_t SkRlinkSendNewJsonMessage(const char *action, const char *operate, const char *body);

// 专用命令发送接口
int32_t SkRlinkSendMusicCommand(const char *operate, const char *text);
int32_t SkRlinkSendStoryCommand(const char *operate, const char *text);
int32_t SkRlinkSendCallCommand(const char *operate, const char *text);
int32_t SkRlinkSendTestCommand(const char *operate, const char *body);
```

## 📝 使用示例

### 接收处理示例

```c
// 接收到的JSON字符串
const char *jsonStr = 
    "{"
    "\"code\": 2200,"
    "\"data\": {"
    "\"action\": \"music\","
    "\"operate\": \"start\","
    "\"text\": \"播放音乐\""
    "},"
    "\"message\": \"SUCCEED\","
    "\"sessionId\": \"abc123\","
    "\"sn\": 1"
    "}";

// 自动调用ProcessJsonMessage处理
```

### 发送命令示例

```c
// 发送音乐控制命令
SkRlinkSendMusicCommand("start", "Voice command play music");

// 发送故事控制命令
SkRlinkSendStoryCommand("pause", "Pause story");

// 发送通话控制命令
SkRlinkSendCallCommand("accept", "Accept call");

// 发送自定义命令
SkRlinkSendNewJsonMessage("custom", "test", "Custom message");
```

## ⚠️ 注意事项

1. **字符编码**：所有JSON消息使用UTF-8编码
2. **消息长度**：单个JSON消息不超过2048字节
3. **错误处理**：非2200状态码需要记录错误日志
4. **会话管理**：sessionId用于关联请求和响应
5. **时间戳**：使用毫秒级时间戳确保消息顺序
6. **内存管理**：JSON解析后需要及时释放内存

## 🔍 调试信息

启用调试日志可以查看详细的JSON处理过程：

```c
ESP_LOGI(TAG, "Processing JSON: %s", jsonStr);
ESP_LOGI(TAG, "JSON fields - message: %s, sessionId: %s, sn: %d", messageStr, sessionIdStr, snValue);
ESP_LOGI(TAG, "Received command - action: %s, operate: %s, text: %s", action, operate, text);
```

## 📈 性能优化

1. **JSON解析**：使用cJSON库进行高效解析
2. **内存池**：复用JSON对象减少内存分配
3. **异步处理**：JSON处理在独立任务中进行
4. **错误恢复**：解析失败时自动重试机制

## 🔗 API接口说明

### 发送接口

```c
/**
 * @brief 发送通用JSON消息
 * @param action 动作类型
 * @param operate 操作类型
 * @param body 消息体内容
 * @return SK_RET_SUCCESS成功，其他失败
 */
int32_t SkRlinkSendNewJsonMessage(const char *action, const char *operate, const char *body);

/**
 * @brief 发送音乐控制命令
 * @param operate 操作类型（start/pause/resume/stop/next/prev）
 * @param text 描述文本
 * @return SK_RET_SUCCESS成功，其他失败
 */
int32_t SkRlinkSendMusicCommand(const char *operate, const char *text);

/**
 * @brief 发送故事控制命令
 * @param operate 操作类型（start/pause/resume/stop/next/prev）
 * @param text 描述文本
 * @return SK_RET_SUCCESS成功，其他失败
 */
int32_t SkRlinkSendStoryCommand(const char *operate, const char *text);

/**
 * @brief 发送通话控制命令
 * @param operate 操作类型（handshake/accept/reject/start/stop）
 * @param text 描述文本
 * @return SK_RET_SUCCESS成功，其他失败
 */
int32_t SkRlinkSendCallCommand(const char *operate, const char *text);

/**
 * @brief 发送测试命令
 * @param operate 操作类型
 * @param body 消息体内容
 * @return SK_RET_SUCCESS成功，其他失败
 */
int32_t SkRlinkSendTestCommand(const char *operate, const char *body);
```

### 返回值说明

| 返回值 | 含义 | 说明 |
|--------|------|------|
| `SK_RET_SUCCESS` | 成功 | 消息发送成功 |
| `SK_RET_NOT_READY` | 未就绪 | WebSocket未连接 |
| `SK_RET_INVALID_PARAM` | 参数错误 | 传入参数无效 |
| `SK_RET_NO_MEMORY` | 内存不足 | JSON对象创建失败 |
| `SK_RET_FAIL` | 发送失败 | 网络发送失败 |

## 🧪 测试用例

### 基本功能测试

```c
// 测试音乐控制
SkRlinkSendMusicCommand("start", "Test music start");
SkRlinkSendMusicCommand("pause", "Test music pause");
SkRlinkSendMusicCommand("stop", "Test music stop");

// 测试故事控制
SkRlinkSendStoryCommand("start", "Test story start");
SkRlinkSendStoryCommand("next", "Test next story");

// 测试通话控制
SkRlinkSendCallCommand("handshake", "Test call handshake");
SkRlinkSendCallCommand("accept", "Test call accept");

// 测试自定义命令
SkRlinkSendNewJsonMessage("custom", "test", "Custom test message");
```

### 错误处理测试

```c
// 测试空参数
int32_t ret = SkRlinkSendMusicCommand(NULL, "test");
assert(ret == SK_RET_INVALID_PARAM);

// 测试WebSocket未连接
// 断开WebSocket连接后测试
ret = SkRlinkSendMusicCommand("start", "test");
assert(ret == SK_RET_NOT_READY);
```

## 📋 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0 | 2025-01-08 | 初始版本，支持新JSON格式 |
| v1.1 | 2025-01-08 | 删除旧格式支持，优化代码结构 |

## 🤝 贡献指南

1. 添加新的action类型时，需要在`ProcessJsonCommand`函数中添加对应的处理分支
2. 添加新的operate类型时，需要在对应的处理函数中添加支持
3. 修改JSON格式时，需要同步更新文档和测试用例
4. 所有修改都需要通过单元测试验证
