# SmartKid终端 JSON格式示例

## 📋 概述

本文档提供SmartKid终端JSON协议的具体示例，展示各种场景下的消息格式。

## 🎵 音乐控制示例

### 接收格式（云端 → 终端）

```json
{
  "code": 2200,
  "data": {
    "action": "music",
    "operate": "start",
    "text": "播放音乐"
  },
  "message": "SUCCEED",
  "sessionId": "music_session_001",
  "sn": 1
}
```

### 发送格式（终端 → 云端）

```json
{
  "action": "music",
  "operate": "start",
  "requestId": "1",
  "timestamp": 1753774119,
  "body": "Voice command play music"
}
```

## 📚 故事播放示例

### 普通故事播放

**接收格式：**
```json
{
  "code": 2200,
  "data": {
    "action": "story",
    "operate": "start",
    "text": "讲故事"
  },
  "message": "SUCCEED",
  "sessionId": "story_session_001",
  "sn": 2
}
```

### AI讲故事（原mock_story）

**接收格式：**
```json
{
  "code": 2200,
  "data": {
    "action": "story",
    "operate": "start",
    "text": "听AI讲故事"
  },
  "message": "SUCCEED",
  "sessionId": "ai_story_session_001",
  "sn": 3
}
```

**或者使用原来的action名称（向后兼容）：**
```json
{
  "code": 2200,
  "data": {
    "action": "mock_story",
    "operate": "start",
    "text": "听AI讲故事"
  },
  "message": "SUCCEED",
  "sessionId": "ai_story_session_002",
  "sn": 4
}
```

**发送格式：**
```json
{
  "action": "story",
  "operate": "start",
  "requestId": "2",
  "timestamp": 1753774120,
  "body": "AI story request"
}
```

## 📞 通话控制示例

### 发起通话

**接收格式：**
```json
{
  "code": 2200,
  "data": {
    "action": "call",
    "operate": "handshake",
    "text": "发起通话"
  },
  "message": "SUCCEED",
  "sessionId": "call_session_001",
  "sn": 5
}
```

### 接受通话

**发送格式：**
```json
{
  "action": "call",
  "operate": "accept",
  "requestId": "3",
  "timestamp": 1753774121,
  "body": "Accept incoming call"
}
```

## ⚠️ 错误处理示例

### 错误响应

```json
{
  "code": 4000,
  "data": {
    "action": "music",
    "operate": "start",
    "text": "播放失败"
  },
  "message": "ERROR",
  "sessionId": "error_session_001",
  "sn": 6
}
```

## 🔄 处理逻辑说明

### Story Action 处理逻辑

```c
// 在ProcessStoryCommand函数中的处理逻辑
if (strcmp(operate, "start") == 0) {
    // 检查是否为AI讲故事
    if (text && (strstr(text, "AI讲故事") != NULL || strstr(text, "听AI讲故事") != NULL)) {
        ESP_LOGI(TAG, "Story: AI Story Start - %s", text);
        // AI讲故事使用聊天模式
        SkSmSendEvent(NULL, SM_EVENT_CMD, SPEECH_CMD_EVENT_CHAT, 0, 0);
    } else {
        ESP_LOGI(TAG, "Story: Regular Story Start - %s", text ? text : "");
        // 普通故事播放逻辑
    }
}
```

### Action 映射关系

| 接收Action | 处理函数 | 说明 |
|------------|----------|------|
| `music` | `ProcessMusicCommand` | 音乐控制 |
| `story` | `ProcessStoryCommand` | 故事播放（包括AI讲故事） |
| `mock_story` | `ProcessStoryCommand` | AI讲故事（向后兼容） |
| `call` | `ProcessCallCommand` | 通话控制 |

### 状态机事件映射

| 操作类型 | 状态机事件 | 说明 |
|----------|------------|------|
| 音乐开始 | `SPEECH_CMD_EVENT_MUSIC` | 切换到音乐状态 |
| AI讲故事 | `SPEECH_CMD_EVENT_CHAT` | 切换到聊天状态 |
| 通话握手 | `SPEECH_CMD_EVENT_CALL` | 切换到通话状态 |
| 暂停 | `SPEECH_CMD_EVENT_PAUSE` | 暂停当前操作 |
| 恢复 | `SPEECH_CMD_EVENT_RESUME` | 恢复当前操作 |
| 停止 | `SPEECH_CMD_EVENT_QUIT` | 退出当前状态 |

## 🧪 测试命令

### 使用C代码测试

```c
// 测试音乐控制
SkRlinkSendMusicCommand("start", "Test music");
SkRlinkSendMusicCommand("pause", "Pause music");
SkRlinkSendMusicCommand("stop", "Stop music");

// 测试普通故事
SkRlinkSendStoryCommand("start", "Regular story");

// 测试AI讲故事
SkRlinkSendStoryCommand("start", "听AI讲故事");

// 测试通话
SkRlinkSendCallCommand("handshake", "Start call");
SkRlinkSendCallCommand("accept", "Accept call");
```

## 📝 注意事项

1. **向后兼容**：`mock_story` action 仍然被支持，会被重定向到 `ProcessStoryCommand`
2. **智能识别**：通过 `text` 字段内容自动判断是AI讲故事还是普通故事
3. **统一处理**：所有故事相关功能都在 `ProcessStoryCommand` 中处理
4. **状态机集成**：不同类型的故事会触发不同的状态机事件

## 🔧 代码优化

通过将 `mock_story` 整合到 `story` 中，实现了：

- ✅ **代码简化**：减少了重复的处理函数
- ✅ **逻辑统一**：所有故事功能集中管理
- ✅ **向后兼容**：保持对旧格式的支持
- ✅ **智能分发**：根据内容自动选择处理方式
